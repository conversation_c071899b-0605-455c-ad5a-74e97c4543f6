<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>jqm-parent</artifactId>
        <groupId>com.qudaiji.cloud</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <artifactId>task-dispatch-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>task-dispatch-service</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <lombok.version>1.18.34</lombok.version>
        <spring.boot.version>3.3.4</spring.boot.version>
        <mapstruct.version>1.6.2</mapstruct.version>
    </properties>

    <dependencies>
        <!-- Spring Boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-protection</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-biz-ip</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-mq</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-mybatis</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-redis</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qudaiji.cloud</groupId>
            <artifactId>jqm-spring-boot-starter-file</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.knuddels</groupId>
            <artifactId>jtokkit</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
            <version>4.6.2.B</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2023.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>rdc-snapshots</id>
            <name>rdc-snapshots</name>
            <url>https://packages.aliyun.com/maven/repository/2125088-snapshot-MaoCjz/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>rdc-releases</id>
            <name>rdc-releases</name>
            <url>https://packages.aliyun.com/maven/repository/2125088-release-DFUYjx/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <name>rdc-releases</name>
            <url>https://packages.aliyun.com/maven/repository/2125088-release-DFUYjx/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <name>rdc-snapshots</name>
            <url>https://packages.aliyun.com/maven/repository/2125088-snapshot-MaoCjz/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <skip>false</skip> <!-- 确保编译不会被跳过 -->
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>