package com.qudaiji.cloud.task.module.notify.controller;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaMessage;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.qudaiji.cloud.task.module.notify.handler.SubscribeMsgChangeEventHandler;
import com.qudaiji.cloud.task.module.notify.handler.SubscribeMsgPopupEventHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Objects;

/**
 * <h1>微信小程序消息通知</h1>
 *
 * <AUTHOR>
 * @date 2024/11/13 16:27
 **/
@Slf4j
@RestController
@RequestMapping("/healthmate/wxmp/message")
public class WxMpMessageNotifyController {
    @Resource
    private WxMaService wxMaService;
    @Resource
    private SubscribeMsgPopupEventHandler subscribeMsgPopupEventHandler;
    @Resource
    private SubscribeMsgChangeEventHandler subscribeMsgChangeEventHandler;

    @GetMapping(value = "/notify", produces = "text/plain;charset=utf-8")
    public String notify(@RequestParam(name = "signature", required = false) String signature,
                         @RequestParam(name = "timestamp", required = false) String timestamp,
                         @RequestParam(name = "nonce", required = false) String nonce,
                         @RequestParam(name = "echostr", required = false) String echostr) {
        log.info("接收到来自微信服务器的认证消息：signature = [{}], timestamp = [{}], nonce = [{}], echostr = [{}]",
                signature, timestamp, nonce, echostr);

        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }
        if (wxMaService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        return "非法请求";
    }


    @PostMapping(value = "/notify", produces = "application/xml; charset=UTF-8")
    public Object notify(@RequestBody String requestBody,
                         @RequestParam(name = "msg_signature", required = false) String msgSignature,
                         @RequestParam(name = "encrypt_type", required = false) String encryptType,
                         @RequestParam(name = "signature", required = false) String signature,
                         @RequestParam("timestamp") String timestamp,
                         @RequestParam("nonce") String nonce) throws IOException {
        log.info("接收微信请求：[msg_signature=[{}], encrypt_type=[{}], signature=[{}], timestamp=[{}], nonce=[{}], requestBody=[{}] ",
                msgSignature, encryptType, signature, timestamp, nonce, requestBody);
        final boolean isJson = Objects.equals(wxMaService.getWxMaConfig().getMsgDataFormat(),
                WxMaConstants.MsgDataFormat.JSON);
        if (StringUtils.isBlank(encryptType)) {
            // 明文传输的消息
            WxMaMessage inMessage;
            if (isJson) {
                inMessage = WxMaMessage.fromJson(requestBody);
            } else {
                inMessage = WxMaMessage.fromXml(requestBody);
            }
            this.route(inMessage);
            return "success";
        }
        if ("aes".equals(encryptType)) {
            // 是aes加密的消息
            WxMaMessage inMessage;
            if (isJson) {
                inMessage = WxMaMessage.fromEncryptedJson(requestBody, wxMaService.getWxMaConfig());
            } else {
                inMessage = WxMaMessage.fromEncryptedXml(requestBody, wxMaService.getWxMaConfig(),
                        timestamp, nonce, msgSignature);
            }
            this.route(inMessage);
            return "success";
        }
        throw new RuntimeException("不可识别的加密类型：" + encryptType);
    }

    private void route(WxMaMessage message) {
        try {
            if (message.getMsgType().equals(WxConsts.XmlMsgType.EVENT) && message.getEvent().equals(WxConsts.EventType.SUBSCRIBE_MSG_POPUP_EVENT)) {
                subscribeMsgPopupEventHandler.handle(message);
            } else if (message.getMsgType().equals(WxConsts.XmlMsgType.EVENT) && message.getEvent().equals(WxConsts.EventType.SUBSCRIBE_MSG_CHANGE_EVENT)) {
                subscribeMsgChangeEventHandler.handle(message);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
