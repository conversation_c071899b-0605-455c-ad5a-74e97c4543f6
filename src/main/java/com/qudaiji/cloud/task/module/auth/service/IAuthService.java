package com.qudaiji.cloud.task.module.auth.service;


import com.qudaiji.cloud.task.module.auth.domain.request.BindPhoneRequest;
import com.qudaiji.cloud.task.module.auth.domain.request.LoginRequest;
import com.qudaiji.cloud.task.module.auth.domain.response.LoginResponse;
import com.qudaiji.cloud.task.module.auth.domain.response.UserInfoResponse;

/**
 * <h1>认证Service</h1>
 *
 * <AUTHOR>
 * @date 2024/6/24 上午11:16
 **/
public interface IAuthService {

    /**
     * <h2>登录</h2>
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse wxmpLogin(LoginRequest loginRequest);

    /**
     * <h2>退出登录</h2>
     *
     * <AUTHOR>
     * @date 2024/7/4 下午10:49
     **/
    void wxmpLogout();


    /**
     * <h1>绑定手机号</h1>
     *
     * @param bindPhoneRequest 绑定手机号请求
     * <AUTHOR>
     * @date 2024/7/25 下午2:31
     **/
    void bindPhone(BindPhoneRequest bindPhoneRequest);

    /**
     * <h1>获取当前用户信息</h1>
     *
     * @return 用户信息
     */
    UserInfoResponse userinfo();
}
