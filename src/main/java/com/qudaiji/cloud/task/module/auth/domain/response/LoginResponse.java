package com.qudaiji.cloud.task.module.auth.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <h1>微信小程序登录响应</h1>
 *
 * <AUTHOR>
 * @date 2024/7/1 下午5:22
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LoginResponse {

    /**
     * 访问令牌
     */
    private String token;

    /**
     * 令牌过期时间戳（毫秒）
     */
    private Long expireTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否为新用户
     */
    private String phone;

    /**
     * 用户状态：是否已实名认证
     */
    private Boolean realNameStatus;
}
