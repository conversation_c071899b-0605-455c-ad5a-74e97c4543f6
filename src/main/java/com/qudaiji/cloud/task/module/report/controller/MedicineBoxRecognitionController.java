package com.qudaiji.cloud.task.module.report.controller;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.web.core.util.WebFrameworkUtils;
import com.qudaiji.cloud.task.module.report.domain.request.MedicineBoxRecognitionPageRequest;
import com.qudaiji.cloud.task.module.report.entity.MedicineBoxRecognition;
import com.qudaiji.cloud.task.module.report.service.IMedicineBoxRecognitionService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * <h1>药盒识别</h1>
 *
 * <AUTHOR>
 * @date 2024/10/29 18:29
 **/
@RestController
@RequestMapping("/healthmate/report/medicineBoxRecognition")
public class MedicineBoxRecognitionController {

    @Resource
    private IMedicineBoxRecognitionService iMedicineBoxRecognitionService;

    /**
     * <h1>药盒识别-分页</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/page")
    public CommonResult<PageResult<MedicineBoxRecognition>> page(@Validated MedicineBoxRecognitionPageRequest request) {
        request.setUserId(WebFrameworkUtils.getLoginUserId());
        PageResult<MedicineBoxRecognition> pageResult = iMedicineBoxRecognitionService.page(request);
        return success(pageResult);
    }

    /**
     * <h1>药盒识别-详情</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/show/{id}")
    public CommonResult<MedicineBoxRecognition> get(@PathVariable(value = "id") Long id) {
        return success(iMedicineBoxRecognitionService.getById(id));
    }

    /**
     * <h1>药盒识别-删除</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @DeleteMapping("/delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable(value = "id") Long id) {
        return success(iMedicineBoxRecognitionService.removeById(id));
    }
}
