package com.qudaiji.cloud.task.module.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qudaiji.cloud.task.module.user.entity.User;
import com.qudaiji.cloud.task.module.user.mapper.UserMapper;
import com.qudaiji.cloud.task.module.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @date 2025/6/20 15:38
 **/
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Override
    public Optional<User> findByOpenId(String openId) {
        log.info("根据openId查询用户信息:{}", openId);
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getOpenid, openId);
        return Optional.ofNullable(this.getOne(wrapper));
    }
}
