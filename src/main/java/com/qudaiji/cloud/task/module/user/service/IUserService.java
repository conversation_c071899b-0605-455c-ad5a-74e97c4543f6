package com.qudaiji.cloud.task.module.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qudaiji.cloud.task.module.user.entity.User;

import java.util.Optional;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @date 2025/6/20 15:37
 **/
public interface IUserService extends IService<User> {
    /**
     * <h1>根据openId查询用户</h1>
     *
     * @param openId openId
     * @return 用户
     */
    Optional<User> findByOpenId(String openId);

    /**
     * <h1>根据手机号查询用户</h1>
     *
     * @param phone 手机号
     * @return 用户
     */
    Optional<User> findByPhone(String phone);

}
