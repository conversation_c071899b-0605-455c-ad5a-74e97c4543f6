package com.qudaiji.cloud.task.module.auth.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.RandomUtil;
import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.task.common.context.UserContext;
import com.qudaiji.cloud.task.common.token.TokenService;
import com.qudaiji.cloud.task.module.auth.domain.request.BindPhoneRequest;
import com.qudaiji.cloud.task.module.auth.domain.request.LoginRequest;
import com.qudaiji.cloud.task.module.auth.domain.response.LoginResponse;
import com.qudaiji.cloud.task.module.auth.domain.response.UserInfoResponse;
import com.qudaiji.cloud.task.module.auth.service.IAuthService;
import com.qudaiji.cloud.task.module.user.entity.User;
import com.qudaiji.cloud.task.module.user.service.IAuthUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.task.common.exception.ErrorCodeConstants.*;


/**
 * <h1>认证Service实现类</h1>
 *
 * <AUTHOR>
 * @date 2024/6/24 上午11:17
 **/
@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {

    @Resource
    private WxMaService wxMaService;
    @Resource
    private IAuthUserService iAuthUserService;
    @Resource
    private TokenService tokenService;

    private final long expireTime = 1000 * 60 * 60 * 24;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse wxmpLogin(LoginRequest loginRequest) {
        WxMaJscode2SessionResult wxMaJscode2SessionResult;
        try {
            wxMaJscode2SessionResult = wxMaService.getUserService().getSessionInfo(loginRequest.getCode());
        } catch (WxErrorException e) {
            log.error("微信登录失败:{}", e.getMessage());
            throw exception(WX_LOGIN_FAIL);
        }
        String openid = wxMaJscode2SessionResult.getOpenid();
        String sessionKey = wxMaJscode2SessionResult.getSessionKey();
        String unionid = wxMaJscode2SessionResult.getUnionid();
        Optional<User> optionalWxUser = iAuthUserService.findByOpenId(openid);

        if (optionalWxUser.isEmpty()) {
            return handleNewWxUser(openid, sessionKey, unionid);
        }
        User authUser = optionalWxUser.get();
        String token = this.generateToken(authUser);
        return new LoginResponse(token, System.currentTimeMillis() + expireTime);
    }


    private LoginResponse handleNewWxUser(String openid, String sessionKey, String unionid) {
        User authUser = new User();
        authUser.setOpenid(openid);
        authUser.setLastLoginTime(LocalDateTime.now());
        authUser.setSessionKey(sessionKey);
        authUser.setUnionid(unionid);
        iAuthUserService.save(authUser);
        String token = this.generateToken(authUser);
        return new LoginResponse(token, System.currentTimeMillis() + expireTime);
    }


    @Override
    public void wxmpLogout() {
        Long userId = UserContext.getCurrentUserId();
        if (userId == null) {
            throw exception(USER_NOT_LOGIN);
        }
        // TODO: 这里需要获取当前请求的token并删除，暂时先不实现
        // 可以通过在UserContext中存储token信息来实现
    }


    private String generateToken(User user) {
        // 使用Redis Token服务生成token，过期时间转换为秒
        long expireSeconds = expireTime / 1000;
        return tokenService.generateToken(user.getId(), UserTypeEnum.MEMBER.getValue(), expireSeconds);
    }

    @Override
    public void bindPhone(BindPhoneRequest bindPhoneRequest) {
        Long userId = UserContext.getCurrentUserId();
        if (userId == null) {
            throw exception(USER_NOT_LOGIN);
        }
        try {
            WxMaPhoneNumberInfo phoneNumber = wxMaService.getUserService().getPhoneNumber(bindPhoneRequest.getCode());
            String purePhoneNumber = phoneNumber.getPurePhoneNumber();
            User user = iAuthUserService.getById(userId);
            user.setPhone(purePhoneNumber);
            iAuthUserService.updateById(user);
        } catch (WxErrorException e) {
            log.error("绑定手机号失败:{}", e.getMessage());
            throw exception(BIND_PHONE_FAIL);
        }
    }

    @Override
    public UserInfoResponse userinfo() {
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        Long userId = UserContext.getCurrentUserId();
        if (userId == null) {
            throw exception(USER_NOT_LOGIN);
        }
        User user = iAuthUserService.getById(userId);
        if (user == null) {
            throw exception(USER_NOT_EXIST);
        }
        BeanUtils.toBean(user, userInfoResponse.getClass());
        return userInfoResponse;
    }

    public static String generateDifyUserId() {
        // 使用 LocalDateTime 获取当前时间并格式化为 yyyyMMddHHmmss
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String dateStr = LocalDateTime.now().format(formatter);

        // 生成 4 位随机数字字符串
        String randomNum = RandomUtil.randomNumbers(4);

        // 拼接字符串
        return "D" + dateStr + randomNum;
    }
}
