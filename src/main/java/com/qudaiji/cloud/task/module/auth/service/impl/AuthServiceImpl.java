package com.qudaiji.cloud.task.module.auth.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.framework.common.enums.UserTypeEnum;
import com.qudaiji.cloud.framework.common.util.object.BeanUtils;
import com.qudaiji.cloud.framework.common.util.servlet.ServletUtils;
import com.qudaiji.cloud.framework.web.core.util.WebFrameworkUtils;
import com.qudaiji.cloud.task.common.token.TokenService;
import com.qudaiji.cloud.task.module.auth.domain.request.BindPhoneRequest;
import com.qudaiji.cloud.task.module.auth.domain.request.LoginRequest;
import com.qudaiji.cloud.task.module.auth.domain.response.LoginResponse;
import com.qudaiji.cloud.task.module.auth.domain.response.UserInfoResponse;
import com.qudaiji.cloud.task.module.auth.service.IAuthService;
import com.qudaiji.cloud.task.module.user.entity.User;
import com.qudaiji.cloud.task.module.user.service.IUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

import static com.qudaiji.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.qudaiji.cloud.task.common.exception.ErrorCodeConstants.*;


/**
 * <h1>认证Service实现类</h1>
 *
 * <AUTHOR>
 * @date 2024/6/24 上午11:17
 **/
@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {

    @Resource
    private WxMaService wxMaService;
    @Resource
    private IUserService userService;
    @Resource
    private TokenService tokenService;

    private final long expireTime = 1000 * 60 * 60 * 24;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse wxmpLogin(LoginRequest loginRequest) {
        log.info("微信小程序登录开始，code: {}", loginRequest.getCode());
        // 1. 参数验证
        if (StrUtil.isBlank(loginRequest.getCode())) {
            log.error("微信登录失败：登录凭证为空");
            throw exception(WX_LOGIN_FAIL);
        }

        // 2. 调用微信接口获取用户信息
        WxMaJscode2SessionResult wxResult = getWxUserInfo(loginRequest.getCode());
        String openid = wxResult.getOpenid();
        String sessionKey = wxResult.getSessionKey();
        String unionid = wxResult.getUnionid();

        log.info("微信登录获取用户信息成功，openid: {}, unionid: {}", openid, unionid);

        // 3. 查找或创建用户
        Optional<User> optionalUser = userService.findByOpenId(openid);
        User user;

        if (optionalUser.isEmpty()) {
            // 新用户注册
            user = createNewUser(openid, sessionKey, unionid);
            log.info("创建新用户成功，userId: {}, openid: {}", user.getId(), openid);
        } else {
            // 老用户登录
            user = optionalUser.get();
            updateUserLoginInfo(user, sessionKey);
            log.info("用户登录成功，userId: {}, openid: {}", user.getId(), openid);
        }

        // 4. 生成token
        String token = generateToken(user);
        long expireTimeMillis = System.currentTimeMillis() + expireTime;
        // 5. 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setExpireTime(expireTimeMillis);
        response.setUserId(user.getId());
        response.setRealNameStatus(user.getRealNameStatus() != null ? user.getRealNameStatus() : false);
        response.setPhone(user.getPhone());
        log.info("微信登录完成，userId: {},, token生成成功", user.getId());
        return response;
    }


    /**
     * 获取微信用户信息
     */
    private WxMaJscode2SessionResult getWxUserInfo(String code) {
        try {
            WxMaJscode2SessionResult result = wxMaService.getUserService().getSessionInfo(code);
            if (result == null || StrUtil.isBlank(result.getOpenid())) {
                log.error("微信登录失败：获取用户信息为空，code: {}", code);
                throw exception(WX_LOGIN_FAIL);
            }
            return result;
        } catch (WxErrorException e) {
            log.error("微信登录失败：调用微信接口异常，code: {}, error: {}", code, e.getMessage(), e);
            throw exception(WX_LOGIN_FAIL);
        }
    }

    /**
     * 创建新用户
     */
    private User createNewUser(String openid, String sessionKey, String unionid) {
        User user = new User();
        user.setOpenid(openid);
        user.setUnionid(unionid);
        user.setSessionKey(sessionKey);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(getClientIp());
        user.setRealNameStatus(false);
        user.setEnabled(true);

        userService.save(user);
        return user;
    }

    /**
     * 更新用户登录信息
     */
    private void updateUserLoginInfo(User user, String sessionKey) {
        user.setSessionKey(sessionKey);
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(getClientIp());
        userService.updateById(user);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            return ServletUtils.getClientIP();
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
            return "unknown";
        }
    }

    /**
     * 从微信获取手机号
     */
    private String getPhoneFromWx(String code) {
        try {
            WxMaPhoneNumberInfo phoneInfo = wxMaService.getUserService().getPhoneNumber(code);
            if (phoneInfo == null || StrUtil.isBlank(phoneInfo.getPurePhoneNumber())) {
                log.error("获取手机号失败：微信返回数据为空，code: {}", code);
                throw exception(BIND_PHONE_FAIL);
            }
            return phoneInfo.getPurePhoneNumber();
        } catch (WxErrorException e) {
            log.error("获取手机号失败：调用微信接口异常，code: {}, error: {}", code, e.getMessage(), e);
            throw exception(BIND_PHONE_FAIL);
        }
    }

    /**
     * 检查手机号是否已被其他用户绑定
     */
    private boolean isPhoneAlreadyBound(String phone, Long currentUserId) {
        Optional<User> existingUser = userService.findByPhone(phone);
        return existingUser.isPresent() && !existingUser.get().getId().equals(currentUserId);
    }

    @Override
    public void wxmpLogout() {
        Long userId = WebFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw exception(USER_NOT_LOGIN);
        }
        // TODO: 这里需要获取当前请求的token并删除，暂时先不实现
        // 可以通过在请求中存储token信息来实现
    }


    private String generateToken(User user) {
        // 使用Redis Token服务生成token，过期时间转换为秒
        long expireSeconds = expireTime / 1000;
        return tokenService.generateToken(user.getId(), UserTypeEnum.MEMBER.getValue(), expireSeconds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPhone(BindPhoneRequest bindPhoneRequest) {
        log.info("开始绑定手机号，code: {}", bindPhoneRequest.getCode());
        // 1. 参数验证
        if (StrUtil.isBlank(bindPhoneRequest.getCode())) {
            log.error("绑定手机号失败：授权码为空");
            throw exception(BIND_PHONE_FAIL);
        }
        // 2. 获取当前用户
        Long userId = WebFrameworkUtils.getLoginUserId();
        if (userId == null) {
            log.error("绑定手机号失败：用户未登录");
            throw exception(USER_NOT_LOGIN);
        }
        User user = userService.getById(userId);
        if (user == null) {
            log.error("绑定手机号失败：用户不存在，userId: {}", userId);
            throw exception(USER_NOT_EXIST);
        }
        // 3. 检查是否已绑定手机号
        if (StrUtil.isNotBlank(user.getPhone())) {
            log.error("用户已绑定手机号，userId: {}, phone: {}", userId, user.getPhone());
            throw exception(USER_HAS_BOUND_PHONE);
        }

        // 4. 调用微信接口获取手机号
        String phoneNumber = getPhoneFromWx(bindPhoneRequest.getCode());

        // 5. 检查手机号是否已被其他用户绑定
        if (isPhoneAlreadyBound(phoneNumber, userId)) {
            log.error("绑定手机号失败：手机号已被其他用户绑定，phone: {}", phoneNumber);
            throw exception(BIND_PHONE_FAIL);
        }
        // 7. 更新用户手机号
        user.setPhone(phoneNumber);
        userService.updateById(user);
        log.info("绑定手机号成功，userId: {}, phone: {}", userId, phoneNumber);
    }

    @Override
    public UserInfoResponse userinfo() {
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        Long userId = WebFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw exception(USER_NOT_LOGIN);
        }
        User user = userService.getById(userId);
        if (user == null) {
            throw exception(USER_NOT_EXIST);
        }
        BeanUtils.toBean(user, userInfoResponse.getClass());
        return userInfoResponse;
    }
}
