package com.qudaiji.cloud.task.module.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qudaiji.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息 DO
 *
 * <AUTHOR>
 */
@Data
@TableName("user")
public class User extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 第三方平台返回的唯一标识符
     */
    private String openid;

    /**
     * 微信等平台 union_id
     */
    private String unionid;

    /**
     * 微信session_key
     */
    private String sessionKey;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 实名认证状态
     */
    private Boolean realNameStatus;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 开户行地址
     */
    private String bankBranchAddress;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别 1男 2女
     */
    private Integer gender;

    /**
     * 最近一次登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最近一次登录 IP
     */
    private String lastLoginIp;

    /**
     * 启用状态
     */
    private Boolean enabled;

    /**
     * 备注
     */
    private String remark;
}