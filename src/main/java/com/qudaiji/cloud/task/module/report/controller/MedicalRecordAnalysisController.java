package com.qudaiji.cloud.task.module.report.controller;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.web.core.util.WebFrameworkUtils;
import com.qudaiji.cloud.task.module.report.domain.request.MedicalRecordAnalysisPageRequest;
import com.qudaiji.cloud.task.module.report.entity.MedicalRecordAnalysis;
import com.qudaiji.cloud.task.module.report.service.IMedicalRecordAnalysisService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * <h1>病历解读</h1>
 *
 * <AUTHOR>
 * @date 2024/10/29 18:29
 **/
@RestController
@RequestMapping("/healthmate/report/medicalRecordAnalysis")
public class MedicalRecordAnalysisController {

    @Resource
    private IMedicalRecordAnalysisService iMedicalRecordAnalysisService;

    /**
     * <h1>病历解读-分页</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/page")
    public CommonResult<PageResult<MedicalRecordAnalysis>> page(@Validated MedicalRecordAnalysisPageRequest request) {
        request.setUserId(UserContext.getCurrentUserId());
        PageResult<MedicalRecordAnalysis> pageResult = iMedicalRecordAnalysisService.page(request);
        return success(pageResult);
    }


    /**
     * <h1>病历解读-详情</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/show/{id}")
    public CommonResult<MedicalRecordAnalysis> get(@PathVariable(value = "id") Long id) {
        return success(iMedicalRecordAnalysisService.getById(id));
    }


    /**
     * <h1>病历解读-删除</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @DeleteMapping("/delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable(value = "id") Long id) {
        return success(iMedicalRecordAnalysisService.removeById(id));
    }
}
