package com.qudaiji.cloud.task.module.auth.controller;


import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.task.common.annotations.NotAuthenticated;
import com.qudaiji.cloud.task.module.auth.domain.request.BindPhoneRequest;
import com.qudaiji.cloud.task.module.auth.domain.request.LoginRequest;
import com.qudaiji.cloud.task.module.auth.domain.response.LoginResponse;
import com.qudaiji.cloud.task.module.auth.domain.response.UserInfoResponse;
import com.qudaiji.cloud.task.module.auth.service.IAuthService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * <h1>认证接口</h1>
 *
 * <AUTHOR>
 * @date 2024/6/24 上午11:15
 **/
@Slf4j
@RestController
@RequestMapping("/healthmate/auth")
public class AuthController {

    @Resource
    private IAuthService iAuthService;

    /**
     * <h2>微信登录</h2>
     *
     * <AUTHOR>
     * @date 2024/7/1 下午3:31
     **/
    @PostMapping("/wxmp/login")
    public CommonResult<LoginResponse> wxmpLogin(@RequestBody LoginRequest loginRequest) {
        log.info("微信登录 入参:{}", loginRequest);
        LoginResponse result = iAuthService.wxmpLogin(loginRequest);
        log.info("微信登录 响应:{}", result);
        return success(result);
    }


    /**
     * <h2>获取当前用户信息</h2>
     *
     * <AUTHOR>
     * @date 2024/7/1 下午3:31
     **/
    @PostMapping("/userinfo")
    public CommonResult<UserInfoResponse> userinfo() {
        log.info("获取当前用户信息");
        UserInfoResponse userinfo = iAuthService.userinfo();
        log.info("获取当前用户信息 响应:{}", userinfo);
        return success(userinfo);
    }

    /**
     * <h2>微信授权绑定手机号</h2>
     *
     * <AUTHOR>
     * @date 2024/7/1 下午3:31
     **/
    @PostMapping("/wxmp/bindPhone")
    public CommonResult<Boolean> bindPhone(@RequestBody BindPhoneRequest bindPhoneRequest) {
        log.info("微信授权绑定手机号 入参:{}", bindPhoneRequest);
        iAuthService.bindPhone(bindPhoneRequest);
        return success(true);
    }

    /**
     * <h2>袋科技注销登录</h2>
     *
     * <AUTHOR>
     * @date 2024/7/1 下午3:31
     **/
    @PostMapping("/wxmp/logout")
    public CommonResult<String> wxmpLogout() {
        log.info("袋科技注销登录");
        iAuthService.wxmpLogout();
        return success("袋科技注销登录成功");
    }
}
