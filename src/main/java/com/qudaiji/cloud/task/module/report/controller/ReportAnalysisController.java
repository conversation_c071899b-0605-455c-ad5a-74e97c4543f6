package com.qudaiji.cloud.task.module.report.controller;

import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.pojo.PageResult;
import com.qudaiji.cloud.framework.web.core.util.WebFrameworkUtils;
import com.qudaiji.cloud.task.module.report.domain.request.ReportAnalysisPageRequest;
import com.qudaiji.cloud.task.module.report.entity.ReportAnalysis;
import com.qudaiji.cloud.task.module.report.service.IReportAnalysisService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.qudaiji.cloud.framework.common.pojo.CommonResult.success;

/**
 * <h1>报告解读</h1>
 *
 * <AUTHOR>
 * @date 2024/10/29 18:29
 **/
@RestController
@RequestMapping("/healthmate/report/reportAnalysis")
public class ReportAnalysisController {

    @Resource
    private IReportAnalysisService iReportAnalysisService;

    /**
     * <h1>报告解读-分页</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/page")
    public CommonResult<PageResult<ReportAnalysis>> page(@Validated ReportAnalysisPageRequest request) {
        request.setUserId(WebFrameworkUtils.getLoginUserId());
        PageResult<ReportAnalysis> pageResult = iReportAnalysisService.page(request);
        return success(pageResult);
    }

    /**
     * <h1>报告解读-详情</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @GetMapping("/show/{id}")
    public CommonResult<ReportAnalysis> get(@PathVariable(value = "id") Long id) {
        return success(iReportAnalysisService.getById(id));
    }

    /**
     * <h1>报告解读-删除</h1>
     *
     * <AUTHOR>
     * @date 2024/11/4 09:54
     **/
    @DeleteMapping("/delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable(value = "id") Long id) {
        return success(iReportAnalysisService.removeById(id));
    }
}
