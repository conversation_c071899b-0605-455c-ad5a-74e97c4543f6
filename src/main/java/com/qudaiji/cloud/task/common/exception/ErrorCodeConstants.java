package com.qudaiji.cloud.task.common.exception;

import com.qudaiji.cloud.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {


    ErrorCode INVALID_APP_KEY = new ErrorCode(200001, "无效的app key");
    ErrorCode PARAMS_ERROR = new ErrorCode(200002, "参数异常");


    ErrorCode CHAT_MESSAGE_PUSH_ERROR = new ErrorCode(201001, "聊天消息推送失败,没有创建SSE连接，请重试。");
    ErrorCode WEBSOCKET_SEND_ERROR = new ErrorCode(201002, "WebSocket 服务端发送消息给客户端失败");


    ErrorCode USER_NOT_LOGIN = new ErrorCode(401, "未登录");
    ErrorCode PHONE_NOT_EXIST = new ErrorCode(202002, "手机号不存在");
    ErrorCode WX_USER_NOT_EXIST = new ErrorCode(202003, "微信用户不存在");
    ErrorCode BIND_PHONE_FAIL = new ErrorCode(202004, "绑定手机号失败");
    ErrorCode USER_NOT_EXIST = new ErrorCode(202005, "用户不存在");
    ErrorCode WX_LOGIN_FAIL = new ErrorCode(202006, "微信登录失败");
    ErrorCode USER_HAS_BOUND_PHONE = new ErrorCode(202007, "用户已绑定手机号");
}
