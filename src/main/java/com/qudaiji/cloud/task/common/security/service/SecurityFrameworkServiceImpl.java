package com.qudaiji.cloud.task.common.security.service;

import com.qudaiji.cloud.task.common.security.core.LoginUser;
import com.qudaiji.cloud.task.common.security.core.util.SecurityFrameworkUtils;
import lombok.AllArgsConstructor;

import static com.qudaiji.cloud.task.common.security.core.util.SecurityFrameworkUtils.getLoginUserId;


/**
 * 默认的 {@link SecurityFrameworkService} 实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class SecurityFrameworkServiceImpl implements SecurityFrameworkService {


    @Override
    public boolean hasPermission(String permission) {
        return hasAnyPermissions(permission);
    }

    @Override
    public boolean hasAnyPermissions(String... permissions) {
        Long userId = getLoginUserId();
        return userId != null;
    }

    @Override
    public boolean hasRole(String role) {
        return hasAnyRoles(role);
    }

    @Override
    public boolean hasAnyRoles(String... roles) {
        Long userId = getLoginUserId();
        return userId != null;
    }

    @Override
    public boolean hasScope(String scope) {
        return hasAnyScopes(scope);
    }

    @Override
    public boolean hasAnyScopes(String... scope) {
        LoginUser user = SecurityFrameworkUtils.getLoginUser();
        return user != null;
    }

}
