package com.qudaiji.cloud.task.common.security.core.filter;

import cn.hutool.core.util.StrUtil;
import com.qudaiji.cloud.framework.common.pojo.CommonResult;
import com.qudaiji.cloud.framework.common.util.servlet.ServletUtils;
import com.qudaiji.cloud.framework.web.core.handler.GlobalExceptionHandler;
import com.qudaiji.cloud.framework.web.core.util.WebFrameworkUtils;
import com.qudaiji.cloud.task.common.security.config.SecurityProperties;
import com.qudaiji.cloud.task.common.security.core.LoginUser;
import com.qudaiji.cloud.task.common.security.core.util.SecurityFrameworkUtils;
import com.qudaiji.cloud.task.common.token.TokenInfo;
import com.qudaiji.cloud.task.common.token.TokenService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Token 过滤器，验证 Redis token 的有效性
 * 验证通过后，获得 {@link LoginUser} 信息，并加入到 Spring Security 上下文
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;

    private final GlobalExceptionHandler globalExceptionHandler;

    private final TokenService tokenService;

    @Override
    @SuppressWarnings("NullableProblems")
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotEmpty(token)) {
            Integer userType = WebFrameworkUtils.getLoginUserType(request);
            try {
                // 1.1 基于 token 构建登录用户
                LoginUser loginUser = buildLoginUserByToken(token, userType);
                // 2. 设置当前用户
                if (loginUser != null) {
                    SecurityFrameworkUtils.setLoginUser(loginUser, request);
                }
            } catch (Throwable ex) {
                CommonResult<?> result = globalExceptionHandler.allExceptionHandler(request, ex);
                ServletUtils.writeJSON(response, result);
                return;
            }
        }

        // 继续过滤链
        chain.doFilter(request, response);
    }

    private LoginUser buildLoginUserByToken(String token, Integer userType) {
        try {
            // 使用Redis Token服务验证token
            TokenInfo tokenInfo = tokenService.validateToken(token);
            if (tokenInfo == null) {
                return null;
            }

            // 构建登录用户
            return new LoginUser()
                    .setId(tokenInfo.getUserId())
                    .setUserType(tokenInfo.getUserType());
        } catch (Exception ex) {
            // 校验 Token 不通过时，考虑到一些接口是无需登录的，所以直接返回 null 即可
            log.debug("Redis token验证失败: {}", ex.getMessage());
            return null;
        }
    }

}
