package com.qudaiji.cloud.task.common.threadpoll;

import com.qudaiji.cloud.framework.mdc.MdcThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <h1>Async异步线程池配置</h1>
 *
 * <AUTHOR>
 * @date 2022/10/1 17:29
 **/
@Slf4j
@EnableAsync
@Configuration
public class HealthmateExecutorConfiguration {
    /**
     * 获取当前机器的核数, 不一定准确 请根据实际场景 CPU密集 || IO 密集
     */
    public static final int cpuNum = Runtime.getRuntime().availableProcessors();

    @Value("${thread.pool.corePoolSize:}")
    private Optional<Integer> corePoolSize;

    @Value("${thread.pool.maxPoolSize:}")
    private Optional<Integer> maxPoolSize;

    @Value("${thread.pool.keepAliveSeconds:}")
    private Optional<Integer> keepAliveSeconds;

    @Value("${thread.pool.queueCapacity:}")
    private Optional<Integer> queueCapacity;

    @Bean(name = "healthmateExecutor")
    public MdcThreadPoolTaskExecutor healthmateExecutor() {
        MdcThreadPoolTaskExecutor taskExecutor =
                new MdcThreadPoolTaskExecutor(
                        corePoolSize.orElse(cpuNum),
                        maxPoolSize.orElse(cpuNum * 2),
                        keepAliveSeconds.orElse(60),
                        queueCapacity.orElse(12),
                        new ThreadPoolExecutor.CallerRunsPolicy(),
                        "Healthmate-Thread-");
        taskExecutor.initialize();
        log.info("异步任务初始化完成....CorePoolSize:{},MaxPoolSize:{},KeepAliveSeconds:{},QueueCapacity:{}", corePoolSize, maxPoolSize, keepAliveSeconds, queueCapacity);
        return taskExecutor;
    }
}
