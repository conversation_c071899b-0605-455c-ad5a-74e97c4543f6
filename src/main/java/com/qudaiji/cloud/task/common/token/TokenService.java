package com.qudaiji.cloud.task.common.token;

/**
 * Token服务接口
 *
 * <AUTHOR>
 */
public interface TokenService {

    /**
     * 生成token
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @param expireSeconds 过期时间（秒）
     * @return token字符串
     */
    String generateToken(Long userId, Integer userType, long expireSeconds);

    /**
     * 验证token并获取用户信息
     *
     * @param token token字符串
     * @return 用户信息，如果token无效或过期返回null
     */
    TokenInfo validateToken(String token);

    /**
     * 删除token
     *
     * @param token token字符串
     */
    void removeToken(String token);

    /**
     * 刷新token过期时间
     *
     * @param token token字符串
     * @param expireSeconds 新的过期时间（秒）
     * @return 是否刷新成功
     */
    boolean refreshToken(String token, long expireSeconds);
}
