package com.qudaiji.cloud.task.common.token;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Token信息
 *
 * <AUTHOR>
 */
@Data
public class TokenInfo implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否已过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);
    }
}
