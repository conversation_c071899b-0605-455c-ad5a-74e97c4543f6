package com.qudaiji.cloud.task.common.interceptor;

import com.qudaiji.cloud.framework.mdc.TraceMdcInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <h1>配置类，注册web层相关组件</h1>
 *
 * <AUTHOR>
 * @date 2024/11/6 16:21
 **/
@Configuration
@Slf4j
public class WebMvcConfiguration implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册自定义拦截器:{}", TraceMdcInterceptor.class.getName());
        //注册自定义拦截器，添加拦截路径和排除拦截路径
        registry.addInterceptor(new TraceMdcInterceptor()).addPathPatterns("/**");
    }
}
