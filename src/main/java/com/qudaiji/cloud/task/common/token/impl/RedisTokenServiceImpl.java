package com.qudaiji.cloud.task.common.token.impl;

import cn.hutool.core.util.IdUtil;
import com.qudaiji.cloud.framework.common.util.json.JsonUtils;
import com.qudaiji.cloud.task.common.token.TokenInfo;
import com.qudaiji.cloud.task.common.token.TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Redis Token服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisTokenServiceImpl implements TokenService {
    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * Token前缀
     */
    private static final String TOKEN_PREFIX = "task:token:";

    @Override
    public String generateToken(Long userId, Integer userType, long expireSeconds) {
        // 生成唯一token
        String token = IdUtil.fastSimpleUUID();

        // 创建token信息
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserId(userId);
        tokenInfo.setUserType(userType);
        tokenInfo.setCreateTime(LocalDateTime.now());
        tokenInfo.setExpireTime(LocalDateTime.now().plusSeconds(expireSeconds));
        // 存储token信息到Redis
        String tokenKey = TOKEN_PREFIX + token;
        redisTemplate.opsForValue().set(tokenKey, JsonUtils.toJsonString(tokenInfo), expireSeconds, TimeUnit.SECONDS);

        log.info("生成token成功: userId={}, userType={}, token={}, expireSeconds={}",
                userId, userType, token, expireSeconds);
        return token;
    }

    @Override
    public TokenInfo validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }

        try {
            String key = TOKEN_PREFIX + token;
            String tokenInfoJson = (String) redisTemplate.opsForValue().get(key);

            if (tokenInfoJson == null) {
                log.debug("Token不存在: {}", token);
                return null;
            }

            TokenInfo tokenInfo = JsonUtils.parseObject(tokenInfoJson, TokenInfo.class);

            // 检查是否过期
            if (tokenInfo.isExpired()) {
                log.debug("Token已过期: {}", token);
                // 删除过期token
                redisTemplate.delete(key);
                return null;
            }

            log.debug("Token验证成功: userId={}, userType={}", tokenInfo.getUserId(), tokenInfo.getUserType());
            return tokenInfo;

        } catch (Exception e) {
            log.error("Token验证失败: {}", token, e);
            return null;
        }
    }

    @Override
    public void removeToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return;
        }

        // 删除token
        String tokenKey = TOKEN_PREFIX + token;
        redisTemplate.delete(tokenKey);

        log.info("删除token: {}", token);
    }

    @Override
    public boolean refreshToken(String token, long expireSeconds) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        try {
            String tokenKey = TOKEN_PREFIX + token;
            String tokenInfoJson = (String) redisTemplate.opsForValue().get(tokenKey);

            if (tokenInfoJson == null) {
                return false;
            }
            TokenInfo tokenInfo = JsonUtils.parseObject(tokenInfoJson, TokenInfo.class);
            // 更新过期时间
            tokenInfo.setExpireTime(LocalDateTime.now().plusSeconds(expireSeconds));
            // 重新存储token信息
            redisTemplate.opsForValue().set(tokenKey, JsonUtils.toJsonString(tokenInfo), expireSeconds, TimeUnit.SECONDS);
            log.info("刷新token成功: {}, expireSeconds={}", token, expireSeconds);
            return true;
        } catch (Exception e) {
            log.error("刷新token失败: {}", token, e);
            return false;
        }
    }
}
