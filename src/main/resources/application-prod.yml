--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ********************************************************************************************************************************************************************************************************************* # MySQL Connector/J 8.X 连接的示例
          username: dll_root
          password: Dll@20220317
        slave: # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: *********************************************************************************************************************************************************************************************************************
          username: dll_root
          password: Dll@20220317

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: r-2ze35mcnfhz8x86gkcpd.redis.rds.aliyuncs.com # 地址
      port: 6379 # 端口
      database: 12 # 数据库索引
      password: y7o5oZy41@ # 密码，建议生产环境开启
--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

wx:
  miniapp:
    appid: wxc586116ed0c37b83
    secret: e201edde399ef39da4cb70afb37af6e9
    token: healthmate2024
    aesKey: KxaUCMd3OKh09xBDDJMM34QbjleRHdvmwA9fPyMsFdU
    msgDataFormat: JSON
    miniprogramState: formal  # 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版

aliyun:
  oss:
    bucket-name: jqm-healthmate-prod