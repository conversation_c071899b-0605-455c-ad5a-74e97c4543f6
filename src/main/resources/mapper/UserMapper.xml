<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qudaiji.cloud.task.module.user.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.qudaiji.cloud.task.module.user.entity.User">
        <!--@mbg.generated-->
        <!--@Table auth_user-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="openid" jdbcType="VARCHAR" property="openid"/>
        <result column="unionid" jdbcType="VARCHAR" property="unionid"/>
        <result column="session_key" jdbcType="VARCHAR" property="sessionKey"/>
        <result column="deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        user_id,
        identity_type,
        openid,
        unionid,
        session_key,
        deleted,
        create_time,
        update_time,
        creator,
        updater,
        remark
    </sql>
</mapper>